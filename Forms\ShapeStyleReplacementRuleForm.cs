using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 形状样式替换规则编辑窗体
    /// </summary>
    public partial class ShapeStyleReplacementRuleForm : Form
    {
        private ShapeStyleReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName;
        private ComboBox cmbSourceShapeType;
        private ComboBox cmbTargetShapeType;
        private CheckBox chkReplaceFillColor;
        private Button btnSourceFillColor;
        private Button btnTargetFillColor;
        private CheckBox chkReplaceBorderColor;
        private Button btnSourceBorderColor;
        private Button btnTargetBorderColor;
        private CheckBox chkReplaceBorderWidth;
        private NumericUpDown numSourceBorderWidth;
        private NumericUpDown numTargetBorderWidth;
        private CheckBox chkIsEnabled;
        private Button btnOK;
        private Button btnCancel;

        private Color _sourceFillColor = Color.White;
        private Color _targetFillColor = Color.White;
        private Color _sourceBorderColor = Color.Black;
        private Color _targetBorderColor = Color.Black;

        public ShapeStyleReplacementRule Rule => _rule;

        public ShapeStyleReplacementRuleForm(ShapeStyleReplacementRule rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new ShapeStyleReplacementRule();

            InitializeForm();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑形状样式替换规则" : "添加形状样式替换规则";
            this.Size = new Size(600, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 200;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth + 120, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源形状类型
            var lblSourceShape = new Label
            {
                Text = "源形状类型:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceShape);

            cmbSourceShapeType = new ComboBox
            {
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbSourceShapeType.Items.AddRange(new string[] { "矩形", "圆形", "三角形", "线条", "文本框", "图片", "其他" });
            cmbSourceShapeType.SelectedIndex = 0;
            this.Controls.Add(cmbSourceShapeType);
            yPos += 35;

            // 目标形状类型
            var lblTargetShape = new Label
            {
                Text = "目标形状类型:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetShape);

            cmbTargetShapeType = new ComboBox
            {
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTargetShapeType.Items.AddRange(new string[] { "矩形", "圆形", "三角形", "线条", "文本框", "图片", "其他" });
            cmbTargetShapeType.SelectedIndex = 0;
            this.Controls.Add(cmbTargetShapeType);
            yPos += 45;

            // 填充颜色替换
            chkReplaceFillColor = new CheckBox
            {
                Text = "替换填充颜色",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceFillColor);

            btnSourceFillColor = new Button
            {
                Text = "源颜色",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(80, 25),
                BackColor = _sourceFillColor
            };
            this.Controls.Add(btnSourceFillColor);

            btnTargetFillColor = new Button
            {
                Text = "目标颜色",
                Location = new Point(leftMargin + 220, yPos),
                Size = new Size(80, 25),
                BackColor = _targetFillColor
            };
            this.Controls.Add(btnTargetFillColor);
            yPos += 35;

            // 边框颜色替换
            chkReplaceBorderColor = new CheckBox
            {
                Text = "替换边框颜色",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceBorderColor);

            btnSourceBorderColor = new Button
            {
                Text = "源颜色",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(80, 25),
                BackColor = _sourceBorderColor
            };
            this.Controls.Add(btnSourceBorderColor);

            btnTargetBorderColor = new Button
            {
                Text = "目标颜色",
                Location = new Point(leftMargin + 220, yPos),
                Size = new Size(80, 25),
                BackColor = _targetBorderColor
            };
            this.Controls.Add(btnTargetBorderColor);
            yPos += 35;

            // 边框宽度替换
            chkReplaceBorderWidth = new CheckBox
            {
                Text = "替换边框宽度",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceBorderWidth);

            var lblSourceWidth = new Label
            {
                Text = "源宽度:",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceWidth);

            numSourceBorderWidth = new NumericUpDown
            {
                Location = new Point(leftMargin + 185, yPos),
                Size = new Size(60, 25),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = 1
            };
            this.Controls.Add(numSourceBorderWidth);

            var lblTargetWidth = new Label
            {
                Text = "目标宽度:",
                Location = new Point(leftMargin + 255, yPos),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetWidth);

            numTargetBorderWidth = new NumericUpDown
            {
                Location = new Point(leftMargin + 320, yPos),
                Size = new Size(60, 25),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 1,
                Value = 1
            };
            this.Controls.Add(numTargetBorderWidth);
            yPos += 45;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnOK.Click += BtnOK_Click;
            btnSourceFillColor.Click += BtnSourceFillColor_Click;
            btnTargetFillColor.Click += BtnTargetFillColor_Click;
            btnSourceBorderColor.Click += BtnSourceBorderColor_Click;
            btnTargetBorderColor.Click += BtnTargetBorderColor_Click;
        }

        /// <summary>
        /// 选择源填充颜色
        /// </summary>
        private void BtnSourceFillColor_Click(object sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _sourceFillColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _sourceFillColor = colorDialog.Color;
                btnSourceFillColor.BackColor = _sourceFillColor;
            }
        }

        /// <summary>
        /// 选择目标填充颜色
        /// </summary>
        private void BtnTargetFillColor_Click(object sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _targetFillColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _targetFillColor = colorDialog.Color;
                btnTargetFillColor.BackColor = _targetFillColor;
            }
        }

        /// <summary>
        /// 选择源边框颜色
        /// </summary>
        private void BtnSourceBorderColor_Click(object sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _sourceBorderColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _sourceBorderColor = colorDialog.Color;
                btnSourceBorderColor.BackColor = _sourceBorderColor;
            }
        }

        /// <summary>
        /// 选择目标边框颜色
        /// </summary>
        private void BtnTargetBorderColor_Click(object sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog { Color = _targetBorderColor };
            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _targetBorderColor = colorDialog.Color;
                btnTargetBorderColor.BackColor = _targetBorderColor;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (!chkReplaceFillColor.Checked && !chkReplaceBorderColor.Checked && !chkReplaceBorderWidth.Checked)
            {
                MessageBox.Show("请至少选择一种替换内容", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;

            // 设置形状类型
            if (!string.IsNullOrEmpty(_rule.SourceShapeType))
            {
                int index = cmbSourceShapeType.Items.IndexOf(_rule.SourceShapeType);
                if (index >= 0) cmbSourceShapeType.SelectedIndex = index;
            }

            if (!string.IsNullOrEmpty(_rule.TargetShapeType))
            {
                int index = cmbTargetShapeType.Items.IndexOf(_rule.TargetShapeType);
                if (index >= 0) cmbTargetShapeType.SelectedIndex = index;
            }

            chkReplaceFillColor.Checked = _rule.ReplaceFillColor;
            chkReplaceBorderColor.Checked = _rule.ReplaceBorderColor;
            chkReplaceBorderWidth.Checked = _rule.ReplaceBorderWidth;

            // 设置颜色
            if (!string.IsNullOrEmpty(_rule.SourceFillColor))
            {
                try
                {
                    _sourceFillColor = ColorTranslator.FromHtml(_rule.SourceFillColor);
                    btnSourceFillColor.BackColor = _sourceFillColor;
                }
                catch { }
            }

            if (!string.IsNullOrEmpty(_rule.TargetFillColor))
            {
                try
                {
                    _targetFillColor = ColorTranslator.FromHtml(_rule.TargetFillColor);
                    btnTargetFillColor.BackColor = _targetFillColor;
                }
                catch { }
            }

            if (!string.IsNullOrEmpty(_rule.SourceBorderColor))
            {
                try
                {
                    _sourceBorderColor = ColorTranslator.FromHtml(_rule.SourceBorderColor);
                    btnSourceBorderColor.BackColor = _sourceBorderColor;
                }
                catch { }
            }

            if (!string.IsNullOrEmpty(_rule.TargetBorderColor))
            {
                try
                {
                    _targetBorderColor = ColorTranslator.FromHtml(_rule.TargetBorderColor);
                    btnTargetBorderColor.BackColor = _targetBorderColor;
                }
                catch { }
            }

            numSourceBorderWidth.Value = (decimal)_rule.SourceBorderWidth;
            numTargetBorderWidth.Value = (decimal)_rule.TargetBorderWidth;
            chkIsEnabled.Checked = _rule.IsEnabled;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceShapeType = cmbSourceShapeType.SelectedItem?.ToString() ?? "";
            _rule.TargetShapeType = cmbTargetShapeType.SelectedItem?.ToString() ?? "";
            _rule.ReplaceFillColor = chkReplaceFillColor.Checked;
            _rule.ReplaceBorderColor = chkReplaceBorderColor.Checked;
            _rule.ReplaceBorderWidth = chkReplaceBorderWidth.Checked;
            _rule.SourceFillColor = ColorTranslator.ToHtml(_sourceFillColor);
            _rule.TargetFillColor = ColorTranslator.ToHtml(_targetFillColor);
            _rule.SourceBorderColor = ColorTranslator.ToHtml(_sourceBorderColor);
            _rule.TargetBorderColor = ColorTranslator.ToHtml(_targetBorderColor);
            _rule.SourceBorderWidth = (float)numSourceBorderWidth.Value;
            _rule.TargetBorderWidth = (float)numTargetBorderWidth.Value;
            _rule.IsEnabled = chkIsEnabled.Checked;
            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
