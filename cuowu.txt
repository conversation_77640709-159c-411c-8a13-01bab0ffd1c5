 PPTPiliangChuli 失败，出现 6 错误和 108 警告 (0.3)
    D:\Cursor\PPTPiliangChuli\PPTPiliangChuli.csproj : warning NU1903: Package 'System.Text.Json' 8.0.4 has a known high severity vulnerability, https://github.com/advisories/GHSA-8g4q-xg66-9fp4
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,81): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,75): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,77): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,69): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,79): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,73): warning CS8625: 无法将 null 字面量转换为非 null 的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,67): warning CS8625: 无法将 null 字面量转换为非 null  的引用类型。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtFindText" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtReplaceText" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkUseRegex" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkCaseSensitive" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkWholeWord" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(15,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbRangeType" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ContentDeletionForm.cs(22,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "_currentSettings" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnSourceColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnTargetColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkApplyToTextColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkApplyToFillColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkApplyToBorderColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkApplyToBackgroundColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(30,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbSourceFont" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbTargetFont" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkExactMatch" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIncludeSubFonts" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbSourceFont" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbTargetFont" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceFontSize" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numSourceFontSize" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numTargetFontSize" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceBoldStyle" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkSourceBold" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkTargetBold" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceItalicStyle" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkSourceItalic" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkTargetItalic" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceUnderlineStyle" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkSourceUnderline" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkTargetUnderline" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(36,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtSourceImagePath" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtTargetImagePath" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnBrowseSource" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnBrowseTarget" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbMatchType" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(26,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(207,28): warning CS8622: “void CustomColorReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(208,37): warning CS8622: “void CustomColorReplacementRuleForm.BtnSourceColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\CustomColorReplacementRuleForm.cs(209,37): warning CS8622: “void CustomColorReplacementRuleForm.BtnTargetColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\TextReplacementRuleForm.cs(196,28): warning CS8622: “void TextReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能 是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(196,38): warning CS8622: “void ImageReplacementRuleForm.BtnBrowseSource_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(197,38): warning CS8622: “void ImageReplacementRuleForm.BtnBrowseTarget_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ImageReplacementRuleForm.cs(198,28): warning CS8622: “void ImageReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可 能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbSourceShapeType" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "cmbTargetShapeType" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceFillColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnSourceFillColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnTargetFillColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceBorderColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnSourceBorderColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnTargetBorderColor" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkReplaceBorderWidth" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numSourceBorderWidth" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numTargetBorderWidth" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(37,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtRuleName" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtSourceText" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "txtTargetText" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkMatchByContent" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkMatchByPosition" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numMatchX" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numMatchY" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numMatchWidth" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "numMatchHeight" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "chkIsEnabled" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnOK" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(29,16): warning CS8618: 在退出构造函数时，不可为 null 的 字段 "btnCancel" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(147,40): error CS1069: 未能在命名空间“System.Data.OleDb”中找到类型名“OleDbConnection”。此类型已转发到程序集“System.Data.OleDb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请考虑添加对该程序集的引用。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(152,38): error CS1069: 未能在命名空间“System.Data.OleDb”中找到类型名“OleDbCommand”。此类型已转发到程序集“System.Data.OleDb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请考虑添加对该程序集的引用。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(161,41): error CS1069: 未能在命名空间“System.Data.OleDb”中找到类型名“OleDbCommand”。此类型已转发到程序集“System.Data.OleDb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请考虑添加对该程序集的引用。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(176,40): error CS1069: 未能在命名空间“System.Data.OleDb”中找到类型名“OleDbConnection”。此类型已转发到程序集“System.Data.OleDb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请考虑添加对该程序集的引用。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(180,62): error CS0103: 当前上下文中不存在名称“OleDbSchemaGuid”
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(187,37): error CS1069: 未能在命名空间“System.Data.OleDb”中找到类型名“OleDbDataAdapter”。此类型已转发到程序集“System.Data.OleDb, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51”。请考虑添加对该程序集的引用。
    D:\Cursor\PPTPiliangChuli\Forms\FontStyleReplacementRuleForm.cs(328,28): warning CS8622: “void FontStyleReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(240,28): warning CS8622: “void TextBoxReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\TextBoxReplacementRuleForm.cs(241,50): warning CS8622: “void TextBoxReplacementRuleForm.ChkMatchByPosition_CheckedChanged(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Services\ExcelImportExportService.cs(240,44): warning CS8602: 解引用可能出现空引用。
    D:\Cursor\PPTPiliangChuli\Forms\FontNameReplacementRuleForm.cs(222,28): warning CS8622: “void FontNameReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(277,28): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnOK_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(278,41): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnSourceFillColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(279,41): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnTargetFillColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托“EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(280,43): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnSourceBorderColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托 “EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ShapeStyleReplacementRuleForm.cs(281,43): warning CS8622: “void ShapeStyleReplacementRuleForm.BtnTargetBorderColor_Click(object sender, EventArgs e)”的参数“sender”类型中引用类型的为 Null 性与目标委托 “EventHandler”不匹配(可能是由于为 Null 性特性)。
    D:\Cursor\PPTPiliangChuli\Forms\ContentDeletionForm.cs(1144,30): warning CS0168: 声明了变量“ex”，但从未使用过
    D:\Cursor\PPTPiliangChuli\Forms\ContentDeletionForm.cs(1180,30): warning CS0168: 声明了变量“ex”，但从未使用过

在 0.8 中生成 失败，出现 6 错误和 109 警告