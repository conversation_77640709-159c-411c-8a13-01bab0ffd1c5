using System.Drawing.Text;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 字体样式替换规则编辑窗体
    /// </summary>
    public partial class FontStyleReplacementRuleForm : Form
    {
        private FontStyleReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName;
        private ComboBox cmbSourceFont;
        private ComboBox cmbTargetFont;
        private CheckBox chkReplaceFontSize;
        private NumericUpDown numSourceFontSize;
        private NumericUpDown numTargetFontSize;
        private CheckBox chkReplaceBoldStyle;
        private CheckBox chkSourceBold;
        private CheckBox chkTargetBold;
        private CheckBox chkReplaceItalicStyle;
        private CheckBox chkSourceItalic;
        private CheckBox chkTargetItalic;
        private CheckBox chkReplaceUnderlineStyle;
        private CheckBox chkSourceUnderline;
        private CheckBox chkTargetUnderline;
        private CheckBox chkIsEnabled;
        private Button btnOK;
        private Button btnCancel;

        public FontStyleReplacementRule Rule => _rule;

        public FontStyleReplacementRuleForm(FontStyleReplacementRule rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new FontStyleReplacementRule();

            InitializeForm();
            LoadAvailableFonts();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑字体样式替换规则" : "添加字体样式替换规则";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 200;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth + 120, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源字体
            var lblSourceFont = new Label
            {
                Text = "源字体:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceFont);

            cmbSourceFont = new ComboBox
            {
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            this.Controls.Add(cmbSourceFont);
            yPos += 35;

            // 目标字体
            var lblTargetFont = new Label
            {
                Text = "目标字体:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetFont);

            cmbTargetFont = new ComboBox
            {
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            this.Controls.Add(cmbTargetFont);
            yPos += 45;

            // 字体大小替换
            chkReplaceFontSize = new CheckBox
            {
                Text = "替换字体大小",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceFontSize);

            var lblSourceSize = new Label
            {
                Text = "源大小:",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceSize);

            numSourceFontSize = new NumericUpDown
            {
                Location = new Point(leftMargin + 185, yPos),
                Size = new Size(60, 25),
                Minimum = 6,
                Maximum = 200,
                Value = 12
            };
            this.Controls.Add(numSourceFontSize);

            var lblTargetSize = new Label
            {
                Text = "目标大小:",
                Location = new Point(leftMargin + 255, yPos),
                Size = new Size(60, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetSize);

            numTargetFontSize = new NumericUpDown
            {
                Location = new Point(leftMargin + 320, yPos),
                Size = new Size(60, 25),
                Minimum = 6,
                Maximum = 200,
                Value = 12
            };
            this.Controls.Add(numTargetFontSize);
            yPos += 35;

            // 粗体样式替换
            chkReplaceBoldStyle = new CheckBox
            {
                Text = "替换粗体样式",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceBoldStyle);

            chkSourceBold = new CheckBox
            {
                Text = "源粗体",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkSourceBold);

            chkTargetBold = new CheckBox
            {
                Text = "目标粗体",
                Location = new Point(leftMargin + 220, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkTargetBold);
            yPos += 35;

            // 斜体样式替换
            chkReplaceItalicStyle = new CheckBox
            {
                Text = "替换斜体样式",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceItalicStyle);

            chkSourceItalic = new CheckBox
            {
                Text = "源斜体",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkSourceItalic);

            chkTargetItalic = new CheckBox
            {
                Text = "目标斜体",
                Location = new Point(leftMargin + 220, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkTargetItalic);
            yPos += 35;

            // 下划线样式替换
            chkReplaceUnderlineStyle = new CheckBox
            {
                Text = "替换下划线样式",
                Location = new Point(leftMargin, yPos),
                Size = new Size(120, 25)
            };
            this.Controls.Add(chkReplaceUnderlineStyle);

            chkSourceUnderline = new CheckBox
            {
                Text = "源下划线",
                Location = new Point(leftMargin + 130, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkSourceUnderline);

            chkTargetUnderline = new CheckBox
            {
                Text = "目标下划线",
                Location = new Point(leftMargin + 220, yPos),
                Size = new Size(80, 25)
            };
            this.Controls.Add(chkTargetUnderline);
            yPos += 45;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 加载可用字体
        /// </summary>
        private void LoadAvailableFonts()
        {
            try
            {
                using var installedFonts = new InstalledFontCollection();
                var fontNames = installedFonts.Families
                    .Select(f => f.Name)
                    .OrderBy(name => name)
                    .ToArray();

                cmbSourceFont.Items.AddRange(fontNames);
                cmbTargetFont.Items.AddRange(fontNames);

                // 设置默认字体
                if (fontNames.Contains("Microsoft YaHei"))
                {
                    cmbSourceFont.Text = "Microsoft YaHei";
                    cmbTargetFont.Text = "Microsoft YaHei";
                }
                else if (fontNames.Length > 0)
                {
                    cmbSourceFont.Text = fontNames[0];
                    cmbTargetFont.Text = fontNames[0];
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载系统字体失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnOK.Click += BtnOK_Click;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (!chkReplaceFontSize.Checked && !chkReplaceBoldStyle.Checked &&
                !chkReplaceItalicStyle.Checked && !chkReplaceUnderlineStyle.Checked)
            {
                MessageBox.Show("请至少选择一种替换内容", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;
            cmbSourceFont.Text = _rule.SourceFontName;
            cmbTargetFont.Text = _rule.TargetFontName;

            chkReplaceFontSize.Checked = _rule.ReplaceFontSize;
            numSourceFontSize.Value = (decimal)_rule.SourceFontSize;
            numTargetFontSize.Value = (decimal)_rule.TargetFontSize;

            chkReplaceBoldStyle.Checked = _rule.ReplaceBoldStyle;
            chkSourceBold.Checked = _rule.SourceBoldStyle;
            chkTargetBold.Checked = _rule.TargetBoldStyle;

            chkReplaceItalicStyle.Checked = _rule.ReplaceItalicStyle;
            chkSourceItalic.Checked = _rule.SourceItalicStyle;
            chkTargetItalic.Checked = _rule.TargetItalicStyle;

            chkReplaceUnderlineStyle.Checked = _rule.ReplaceUnderlineStyle;
            chkSourceUnderline.Checked = _rule.SourceUnderlineStyle;
            chkTargetUnderline.Checked = _rule.TargetUnderlineStyle;

            chkIsEnabled.Checked = _rule.IsEnabled;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceFontName = cmbSourceFont.Text.Trim();
            _rule.TargetFontName = cmbTargetFont.Text.Trim();

            _rule.ReplaceFontSize = chkReplaceFontSize.Checked;
            _rule.SourceFontSize = (float)numSourceFontSize.Value;
            _rule.TargetFontSize = (float)numTargetFontSize.Value;

            _rule.ReplaceBoldStyle = chkReplaceBoldStyle.Checked;
            _rule.SourceBoldStyle = chkSourceBold.Checked;
            _rule.TargetBoldStyle = chkTargetBold.Checked;

            _rule.ReplaceItalicStyle = chkReplaceItalicStyle.Checked;
            _rule.SourceItalicStyle = chkSourceItalic.Checked;
            _rule.TargetItalicStyle = chkTargetItalic.Checked;

            _rule.ReplaceUnderlineStyle = chkReplaceUnderlineStyle.Checked;
            _rule.SourceUnderlineStyle = chkSourceUnderline.Checked;
            _rule.TargetUnderlineStyle = chkTargetUnderline.Checked;

            _rule.IsEnabled = chkIsEnabled.Checked;
            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
