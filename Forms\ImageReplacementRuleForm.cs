using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 图片替换规则编辑窗体
    /// </summary>
    public partial class ImageReplacementRuleForm : Form
    {
        private ImageReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName = null!;
        private TextBox txtSourceImagePath = null!;
        private TextBox txtTargetImagePath = null!;
        private Button btnBrowseSource = null!;
        private Button btnBrowseTarget = null!;
        private ComboBox cmbMatchType = null!;
        private CheckBox chkIsEnabled = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;

        public ImageReplacementRule Rule => _rule;

        public ImageReplacementRuleForm(ImageReplacementRule rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new ImageReplacementRule();

            InitializeForm();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑图片替换规则" : "添加图片替换规则";
            this.Size = new Size(600, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 320;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源图片路径
            var lblSourceImage = new Label
            {
                Text = "源图片路径:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceImage);

            txtSourceImagePath = new TextBox
            {
                Name = "txtSourceImagePath",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth - 80, 25),
                ReadOnly = true
            };
            this.Controls.Add(txtSourceImagePath);

            btnBrowseSource = new Button
            {
                Text = "浏览...",
                Location = new Point(leftMargin + labelWidth + 10 + controlWidth - 75, yPos),
                Size = new Size(70, 25)
            };
            this.Controls.Add(btnBrowseSource);
            yPos += 35;

            // 目标图片路径
            var lblTargetImage = new Label
            {
                Text = "目标图片路径:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetImage);

            txtTargetImagePath = new TextBox
            {
                Name = "txtTargetImagePath",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth - 80, 25),
                ReadOnly = true
            };
            this.Controls.Add(txtTargetImagePath);

            btnBrowseTarget = new Button
            {
                Text = "浏览...",
                Location = new Point(leftMargin + labelWidth + 10 + controlWidth - 75, yPos),
                Size = new Size(70, 25)
            };
            this.Controls.Add(btnBrowseTarget);
            yPos += 35;

            // 匹配方式
            var lblMatchType = new Label
            {
                Text = "匹配方式:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblMatchType);

            cmbMatchType = new ComboBox
            {
                Name = "cmbMatchType",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbMatchType.Items.AddRange(new string[] { "按文件名匹配", "按图片内容匹配", "按图片尺寸匹配" });
            cmbMatchType.SelectedIndex = 0;
            this.Controls.Add(cmbMatchType);
            yPos += 35;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnBrowseSource.Click += BtnBrowseSource_Click;
            btnBrowseTarget.Click += BtnBrowseTarget_Click;
            btnOK.Click += BtnOK_Click;
        }

        /// <summary>
        /// 浏览源图片
        /// </summary>
        private void BtnBrowseSource_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择源图片",
                Filter = "图片文件 (*.jpg;*.jpeg;*.png;*.bmp;*.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                txtSourceImagePath.Text = openFileDialog.FileName;
            }
        }

        /// <summary>
        /// 浏览目标图片
        /// </summary>
        private void BtnBrowseTarget_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "选择目标图片",
                Filter = "图片文件 (*.jpg;*.jpeg;*.png;*.bmp;*.gif)|*.jpg;*.jpeg;*.png;*.bmp;*.gif|所有文件 (*.*)|*.*"
            };

            if (openFileDialog.ShowDialog(this) == DialogResult.OK)
            {
                txtTargetImagePath.Text = openFileDialog.FileName;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSourceImagePath.Text))
            {
                MessageBox.Show("请选择源图片", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                btnBrowseSource.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtTargetImagePath.Text))
            {
                MessageBox.Show("请选择目标图片", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                btnBrowseTarget.Focus();
                return false;
            }

            if (!File.Exists(txtSourceImagePath.Text))
            {
                MessageBox.Show("源图片文件不存在", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                btnBrowseSource.Focus();
                return false;
            }

            if (!File.Exists(txtTargetImagePath.Text))
            {
                MessageBox.Show("目标图片文件不存在", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                btnBrowseTarget.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;
            txtSourceImagePath.Text = _rule.SourceImagePath;
            txtTargetImagePath.Text = _rule.TargetImagePath;
            chkIsEnabled.Checked = _rule.IsEnabled;

            // 设置匹配方式
            if (_rule.MatchByFileName)
                cmbMatchType.SelectedIndex = 0;
            else if (_rule.MatchByContent)
                cmbMatchType.SelectedIndex = 1;
            else if (_rule.MatchBySize)
                cmbMatchType.SelectedIndex = 2;
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceImagePath = txtSourceImagePath.Text.Trim();
            _rule.TargetImagePath = txtTargetImagePath.Text.Trim();
            _rule.IsEnabled = chkIsEnabled.Checked;

            // 设置匹配方式
            _rule.MatchByFileName = cmbMatchType.SelectedIndex == 0;
            _rule.MatchByContent = cmbMatchType.SelectedIndex == 1;
            _rule.MatchBySize = cmbMatchType.SelectedIndex == 2;

            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
