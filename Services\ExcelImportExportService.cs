using System.Data;
using System.Data.OleDb;
using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Services
{
    /// <summary>
    /// Excel导入导出服务类
    /// </summary>
    public class ExcelImportExportService
    {
        /// <summary>
        /// 创建文本替换规则Excel模板
        /// </summary>
        /// <param name="filePath">模板文件保存路径</param>
        public void CreateTextReplacementTemplate(string filePath)
        {
            try
            {
                var dataTable = new DataTable();
                dataTable.Columns.Add("规则名称", typeof(string));
                dataTable.Columns.Add("查找文本", typeof(string));
                dataTable.Columns.Add("替换文本", typeof(string));
                dataTable.Columns.Add("使用正则表达式", typeof(string));
                dataTable.Columns.Add("区分大小写", typeof(string));
                dataTable.Columns.Add("全词匹配", typeof(string));
                dataTable.Columns.Add("是否启用", typeof(string));

                // 添加示例数据
                var row = dataTable.NewRow();
                row["规则名称"] = "示例规则";
                row["查找文本"] = "旧文本";
                row["替换文本"] = "新文本";
                row["使用正则表达式"] = "否";
                row["区分大小写"] = "否";
                row["全词匹配"] = "否";
                row["是否启用"] = "是";
                dataTable.Rows.Add(row);

                ExportDataTableToExcel(dataTable, filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建Excel模板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从Excel文件导入文本替换规则
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        /// <returns>导入的规则列表</returns>
        public List<TextReplacementRule> ImportTextReplacementRules(string filePath)
        {
            try
            {
                var rules = new List<TextReplacementRule>();
                var dataTable = ImportExcelToDataTable(filePath);

                if (dataTable == null || dataTable.Rows.Count == 0)
                    return rules;

                foreach (DataRow row in dataTable.Rows)
                {
                    // 跳过空行或标题行
                    if (IsEmptyRow(row) || IsHeaderRow(row))
                        continue;

                    var rule = new TextReplacementRule
                    {
                        RuleName = GetStringValue(row, "规则名称"),
                        FindText = GetStringValue(row, "查找文本"),
                        ReplaceText = GetStringValue(row, "替换文本"),
                        UseRegex = GetBooleanValue(row, "使用正则表达式"),
                        CaseSensitive = GetBooleanValue(row, "区分大小写"),
                        WholeWord = GetBooleanValue(row, "全词匹配"),
                        IsEnabled = GetBooleanValue(row, "是否启用", true)
                    };

                    // 验证必填字段
                    if (!string.IsNullOrWhiteSpace(rule.FindText))
                    {
                        rules.Add(rule);
                    }
                }

                return rules;
            }
            catch (Exception ex)
            {
                throw new Exception($"导入Excel文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 导出文本替换规则到Excel文件
        /// </summary>
        /// <param name="rules">规则列表</param>
        /// <param name="filePath">导出文件路径</param>
        public void ExportTextReplacementRules(List<TextReplacementRule> rules, string filePath)
        {
            try
            {
                var dataTable = new DataTable();
                dataTable.Columns.Add("规则名称", typeof(string));
                dataTable.Columns.Add("查找文本", typeof(string));
                dataTable.Columns.Add("替换文本", typeof(string));
                dataTable.Columns.Add("使用正则表达式", typeof(string));
                dataTable.Columns.Add("区分大小写", typeof(string));
                dataTable.Columns.Add("全词匹配", typeof(string));
                dataTable.Columns.Add("是否启用", typeof(string));

                foreach (var rule in rules)
                {
                    var row = dataTable.NewRow();
                    row["规则名称"] = rule.RuleName;
                    row["查找文本"] = rule.FindText;
                    row["替换文本"] = rule.ReplaceText;
                    row["使用正则表达式"] = rule.UseRegex ? "是" : "否";
                    row["区分大小写"] = rule.CaseSensitive ? "是" : "否";
                    row["全词匹配"] = rule.WholeWord ? "是" : "否";
                    row["是否启用"] = rule.IsEnabled ? "是" : "否";
                    dataTable.Rows.Add(row);
                }

                ExportDataTableToExcel(dataTable, filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"导出Excel文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将DataTable导出到Excel文件
        /// </summary>
        private void ExportDataTableToExcel(DataTable dataTable, string filePath)
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var connectionString = GetConnectionString(filePath, true);

            using var connection = new OleDbConnection(connectionString);
            connection.Open();

            // 创建表结构
            var createTableSql = BuildCreateTableSql(dataTable);
            using (var command = new OleDbCommand(createTableSql, connection))
            {
                command.ExecuteNonQuery();
            }

            // 插入数据
            foreach (DataRow row in dataTable.Rows)
            {
                var insertSql = BuildInsertSql(dataTable, row);
                using var command = new OleDbCommand(insertSql, connection);
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 从Excel文件导入到DataTable
        /// </summary>
        private DataTable ImportExcelToDataTable(string filePath)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            var connectionString = GetConnectionString(filePath, false);

            using var connection = new OleDbConnection(connectionString);
            connection.Open();

            // 获取第一个工作表名称
            var schemaTable = connection.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
            if (schemaTable == null || schemaTable.Rows.Count == 0)
                throw new Exception("Excel文件中没有找到工作表");

            var sheetName = schemaTable.Rows[0]["TABLE_NAME"].ToString();
            var sql = $"SELECT * FROM [{sheetName}]";

            using var adapter = new OleDbDataAdapter(sql, connection);
            var dataTable = new DataTable();
            adapter.Fill(dataTable);

            return dataTable;
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        private string GetConnectionString(string filePath, bool isExport)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            var provider = extension == ".xlsx" ?
                "Microsoft.ACE.OLEDB.12.0" :
                "Microsoft.Jet.OLEDB.4.0";

            var properties = extension == ".xlsx" ?
                "Excel 12.0 Xml;HDR=YES" :
                "Excel 8.0;HDR=YES";

            if (isExport)
            {
                properties += ";IMEX=0";
            }
            else
            {
                properties += ";IMEX=1";
            }

            return $"Provider={provider};Data Source={filePath};Extended Properties=\"{properties}\"";
        }

        /// <summary>
        /// 构建创建表的SQL语句
        /// </summary>
        private string BuildCreateTableSql(DataTable dataTable)
        {
            var columns = dataTable.Columns.Cast<DataColumn>()
                .Select(column => $"[{column.ColumnName}] TEXT");

            return $"CREATE TABLE [Sheet1] ({string.Join(", ", columns)})";
        }

        /// <summary>
        /// 构建插入数据的SQL语句
        /// </summary>
        private string BuildInsertSql(DataTable dataTable, DataRow row)
        {
            var columnNames = string.Join(", ", dataTable.Columns.Cast<DataColumn>()
                .Select(column => $"[{column.ColumnName}]"));

            var values = string.Join(", ", row.ItemArray
                .Select(value => $"'{value?.ToString().Replace("'", "''")}'"));

            return $"INSERT INTO [Sheet1] ({columnNames}) VALUES ({values})";
        }

        /// <summary>
        /// 检查是否为空行
        /// </summary>
        private bool IsEmptyRow(DataRow row)
        {
            return row.ItemArray.All(field => field == null || string.IsNullOrWhiteSpace(field.ToString()));
        }

        /// <summary>
        /// 检查是否为标题行
        /// </summary>
        private bool IsHeaderRow(DataRow row)
        {
            var firstCell = row.ItemArray.FirstOrDefault()?.ToString();
            return string.Equals(firstCell, "规则名称", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        private string GetStringValue(DataRow row, string columnName)
        {
            try
            {
                var table = row.Table;
                if (table.Columns.Contains(columnName))
                {
                    return row[columnName]?.ToString() ?? "";
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取布尔值
        /// </summary>
        private bool GetBooleanValue(DataRow row, string columnName, bool defaultValue = false)
        {
            try
            {
                var value = GetStringValue(row, columnName);
                if (string.IsNullOrWhiteSpace(value))
                    return defaultValue;

                return value.Equals("是", StringComparison.OrdinalIgnoreCase) ||
                       value.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                       value.Equals("1", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
