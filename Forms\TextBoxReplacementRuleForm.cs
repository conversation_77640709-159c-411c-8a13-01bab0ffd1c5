using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文本框替换规则编辑窗体
    /// </summary>
    public partial class TextBoxReplacementRuleForm : Form
    {
        private TextBoxReplacementRule _rule;
        private bool _isEditMode;

        // 控件
        private TextBox txtRuleName;
        private TextBox txtSourceText;
        private TextBox txtTargetText;
        private CheckBox chkMatchByContent;
        private CheckBox chkMatchByPosition;
        private NumericUpDown numMatchX;
        private NumericUpDown numMatchY;
        private NumericUpDown numMatchWidth;
        private NumericUpDown numMatchHeight;
        private CheckBox chkIsEnabled;
        private Button btnOK;
        private Button btnCancel;

        public TextBoxReplacementRule Rule => _rule;

        public TextBoxReplacementRuleForm(TextBoxReplacementRule rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new TextBoxReplacementRule();

            InitializeForm();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑文本框替换规则" : "添加文本框替换规则";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;

            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 320;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 源文本内容
            var lblSourceText = new Label
            {
                Text = "源文本内容:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblSourceText);

            txtSourceText = new TextBox
            {
                Name = "txtSourceText",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(txtSourceText);
            yPos += 70;

            // 目标文本内容
            var lblTargetText = new Label
            {
                Text = "目标文本内容:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblTargetText);

            txtTargetText = new TextBox
            {
                Name = "txtTargetText",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(txtTargetText);
            yPos += 70;

            // 匹配方式
            var grpMatchType = new GroupBox
            {
                Text = "匹配方式",
                Location = new Point(leftMargin, yPos),
                Size = new Size(controlWidth + labelWidth + 10, 120)
            };
            this.Controls.Add(grpMatchType);

            chkMatchByContent = new CheckBox
            {
                Text = "按文本内容匹配",
                Location = new Point(15, 25),
                Size = new Size(150, 25),
                Checked = true
            };
            grpMatchType.Controls.Add(chkMatchByContent);

            chkMatchByPosition = new CheckBox
            {
                Text = "按文本框位置匹配",
                Location = new Point(15, 55),
                Size = new Size(150, 25)
            };
            grpMatchType.Controls.Add(chkMatchByPosition);

            // 位置参数
            var lblX = new Label { Text = "X:", Location = new Point(200, 25), Size = new Size(20, 25) };
            grpMatchType.Controls.Add(lblX);
            numMatchX = new NumericUpDown
            {
                Location = new Point(225, 25),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 10000,
                DecimalPlaces = 1
            };
            grpMatchType.Controls.Add(numMatchX);

            var lblY = new Label { Text = "Y:", Location = new Point(320, 25), Size = new Size(20, 25) };
            grpMatchType.Controls.Add(lblY);
            numMatchY = new NumericUpDown
            {
                Location = new Point(345, 25),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 10000,
                DecimalPlaces = 1
            };
            grpMatchType.Controls.Add(numMatchY);

            var lblWidth = new Label { Text = "宽度:", Location = new Point(200, 55), Size = new Size(40, 25) };
            grpMatchType.Controls.Add(lblWidth);
            numMatchWidth = new NumericUpDown
            {
                Location = new Point(245, 55),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 10000,
                DecimalPlaces = 1
            };
            grpMatchType.Controls.Add(numMatchWidth);

            var lblHeight = new Label { Text = "高度:", Location = new Point(340, 55), Size = new Size(40, 25) };
            grpMatchType.Controls.Add(lblHeight);
            numMatchHeight = new NumericUpDown
            {
                Location = new Point(385, 55),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 10000,
                DecimalPlaces = 1
            };
            grpMatchType.Controls.Add(numMatchHeight);

            yPos += 130;

            // 是否启用
            chkIsEnabled = new CheckBox
            {
                Text = "启用此规则",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(150, 25),
                Checked = true
            };
            this.Controls.Add(chkIsEnabled);
            yPos += 50;

            // 按钮
            btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            this.Controls.Add(btnOK);

            btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, this.Height - 80),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            btnOK.Click += BtnOK_Click;
            chkMatchByPosition.CheckedChanged += ChkMatchByPosition_CheckedChanged;
        }

        /// <summary>
        /// 位置匹配选项变化事件
        /// </summary>
        private void ChkMatchByPosition_CheckedChanged(object sender, EventArgs e)
        {
            bool enabled = chkMatchByPosition.Checked;
            numMatchX.Enabled = enabled;
            numMatchY.Enabled = enabled;
            numMatchWidth.Enabled = enabled;
            numMatchHeight.Enabled = enabled;
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRuleData();
            }
        }

        /// <summary>
        /// 验证输入
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtRuleName.Text))
            {
                MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRuleName.Focus();
                return false;
            }

            if (!chkMatchByContent.Checked && !chkMatchByPosition.Checked)
            {
                MessageBox.Show("请至少选择一种匹配方式", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (chkMatchByContent.Checked && string.IsNullOrWhiteSpace(txtSourceText.Text))
            {
                MessageBox.Show("选择按内容匹配时，请输入源文本内容", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSourceText.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            txtRuleName.Text = _rule.RuleName;
            txtSourceText.Text = _rule.SourceTextContent;
            txtTargetText.Text = _rule.TargetTextContent;
            chkMatchByContent.Checked = _rule.MatchByContent;
            chkMatchByPosition.Checked = _rule.MatchByPosition;
            numMatchX.Value = (decimal)_rule.MatchX;
            numMatchY.Value = (decimal)_rule.MatchY;
            numMatchWidth.Value = (decimal)_rule.MatchWidth;
            numMatchHeight.Value = (decimal)_rule.MatchHeight;
            chkIsEnabled.Checked = _rule.IsEnabled;

            // 触发位置匹配选项变化事件
            ChkMatchByPosition_CheckedChanged(chkMatchByPosition, EventArgs.Empty);
        }

        /// <summary>
        /// 保存规则数据
        /// </summary>
        private void SaveRuleData()
        {
            _rule.RuleName = txtRuleName.Text.Trim();
            _rule.SourceTextContent = txtSourceText.Text.Trim();
            _rule.TargetTextContent = txtTargetText.Text.Trim();
            _rule.MatchByContent = chkMatchByContent.Checked;
            _rule.MatchByPosition = chkMatchByPosition.Checked;
            _rule.MatchX = (float)numMatchX.Value;
            _rule.MatchY = (float)numMatchY.Value;
            _rule.MatchWidth = (float)numMatchWidth.Value;
            _rule.MatchHeight = (float)numMatchHeight.Value;
            _rule.IsEnabled = chkIsEnabled.Checked;
            _rule.CreatedTime = _isEditMode ? _rule.CreatedTime : DateTime.Now;
        }
    }
}
