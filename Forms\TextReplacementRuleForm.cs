using PPTPiliangChuli.Models;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// 文本替换规则编辑窗体
    /// </summary>
    public partial class TextReplacementRuleForm : Form
    {
        private TextReplacementRule _rule;
        private bool _isEditMode;

        public TextReplacementRule Rule => _rule;

        public TextReplacementRuleForm(TextReplacementRule rule = null)
        {
            InitializeComponent();
            _isEditMode = rule != null;
            _rule = rule ?? new TextReplacementRule();

            InitializeForm();
            LoadRuleData();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            this.Text = _isEditMode ? "编辑文本替换规则" : "添加文本替换规则";

            // 设置窗体属性
            this.Size = new Size(500, 400);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.ShowIcon = false;
            this.ShowInTaskbar = false;

            // 创建控件
            CreateControls();
            SetupEventHandlers();
        }

        /// <summary>
        /// 创建控件
        /// </summary>
        private void CreateControls()
        {
            int yPos = 20;
            int leftMargin = 20;
            int labelWidth = 100;
            int controlWidth = 320;

            // 规则名称
            var lblRuleName = new Label
            {
                Text = "规则名称:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblRuleName);

            txtRuleName = new TextBox
            {
                Name = "txtRuleName",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25)
            };
            this.Controls.Add(txtRuleName);
            yPos += 35;

            // 查找文本
            var lblFindText = new Label
            {
                Text = "查找文本:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblFindText);

            txtFindText = new TextBox
            {
                Name = "txtFindText",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 25),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            txtFindText.Size = new Size(controlWidth, 60);
            this.Controls.Add(txtFindText);
            yPos += 70;

            // 替换文本
            var lblReplaceText = new Label
            {
                Text = "替换文本:",
                Location = new Point(leftMargin, yPos),
                Size = new Size(labelWidth, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };
            this.Controls.Add(lblReplaceText);

            txtReplaceText = new TextBox
            {
                Name = "txtReplaceText",
                Location = new Point(leftMargin + labelWidth + 10, yPos),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            this.Controls.Add(txtReplaceText);
            yPos += 80;

            // 选项
            var grpOptions = new GroupBox
            {
                Text = "选项",
                Location = new Point(leftMargin, yPos),
                Size = new Size(controlWidth + labelWidth + 10, 120)
            };
            this.Controls.Add(grpOptions);

            chkUseRegex = new CheckBox
            {
                Name = "chkUseRegex",
                Text = "使用正则表达式",
                Location = new Point(15, 25),
                Size = new Size(150, 25)
            };
            grpOptions.Controls.Add(chkUseRegex);

            chkCaseSensitive = new CheckBox
            {
                Name = "chkCaseSensitive",
                Text = "区分大小写",
                Location = new Point(15, 55),
                Size = new Size(150, 25)
            };
            grpOptions.Controls.Add(chkCaseSensitive);

            chkWholeWord = new CheckBox
            {
                Name = "chkWholeWord",
                Text = "全词匹配",
                Location = new Point(200, 25),
                Size = new Size(150, 25)
            };
            grpOptions.Controls.Add(chkWholeWord);

            chkIsEnabled = new CheckBox
            {
                Name = "chkIsEnabled",
                Text = "启用此规则",
                Location = new Point(200, 55),
                Size = new Size(150, 25),
                Checked = true
            };
            grpOptions.Controls.Add(chkIsEnabled);

            yPos += 130;

            // 替换范围
            var grpRange = new GroupBox
            {
                Text = "替换范围",
                Location = new Point(leftMargin, yPos),
                Size = new Size(controlWidth + labelWidth + 10, 80)
            };
            this.Controls.Add(grpRange);

            cmbRangeType = new ComboBox
            {
                Name = "cmbRangeType",
                Location = new Point(15, 25),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbRangeType.Items.AddRange(new[] { "全部", "仅标题", "仅内容", "仅备注", "自定义范围" });
            cmbRangeType.SelectedIndex = 0;
            grpRange.Controls.Add(cmbRangeType);

            yPos += 90;

            // 按钮
            var btnOK = new Button
            {
                Text = "确定",
                Location = new Point(this.Width - 180, yPos),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;
            this.Controls.Add(btnOK);

            var btnCancel = new Button
            {
                Text = "取消",
                Location = new Point(this.Width - 95, yPos),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };
            this.Controls.Add(btnCancel);

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;

            // 调整窗体高度
            this.Height = yPos + 80;
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            // 可以在这里添加其他事件处理程序
        }

        /// <summary>
        /// 加载规则数据
        /// </summary>
        private void LoadRuleData()
        {
            if (_rule != null)
            {
                txtRuleName.Text = _rule.RuleName;
                txtFindText.Text = _rule.FindText;
                txtReplaceText.Text = _rule.ReplaceText;
                chkUseRegex.Checked = _rule.UseRegex;
                chkCaseSensitive.Checked = _rule.CaseSensitive;
                chkWholeWord.Checked = _rule.WholeWord;
                chkIsEnabled.Checked = _rule.IsEnabled;
                cmbRangeType.SelectedIndex = (int)_rule.RangeType;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRuleName.Text))
                {
                    MessageBox.Show("请输入规则名称", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRuleName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtFindText.Text))
                {
                    MessageBox.Show("请输入查找文本", "验证失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtFindText.Focus();
                    return;
                }

                // 保存数据
                _rule.RuleName = txtRuleName.Text.Trim();
                _rule.FindText = txtFindText.Text;
                _rule.ReplaceText = txtReplaceText.Text;
                _rule.UseRegex = chkUseRegex.Checked;
                _rule.CaseSensitive = chkCaseSensitive.Checked;
                _rule.WholeWord = chkWholeWord.Checked;
                _rule.IsEnabled = chkIsEnabled.Checked;
                _rule.RangeType = (TextReplacementRangeType)cmbRangeType.SelectedIndex;

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存规则时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 控件声明
        private TextBox txtRuleName;
        private TextBox txtFindText;
        private TextBox txtReplaceText;
        private CheckBox chkUseRegex;
        private CheckBox chkCaseSensitive;
        private CheckBox chkWholeWord;
        private CheckBox chkIsEnabled;
        private ComboBox cmbRangeType;
    }
}
